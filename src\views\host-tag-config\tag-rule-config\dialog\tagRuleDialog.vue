<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    width="1200px"
    :close-on-click-modal="false"
    :show-close="true"
    @close="$emit('close')"
    append-to-body
  >
    <!-- 标签规则表单 -->
    <div class="form-container">
      <div class="form-header">
        <h4>{{ headerTitle }}</h4>
      </div>

      <div class="batch-table">
        <el-table :data="batchForms" border style="width: 100%">
          <el-table-column label="序号" width="60" align="center">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>

          <el-table-column label="CDN类型" width="120">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.cdn_type"
                placeholder="选择"
                size="small"
                style="width: 100%"
                :class="{ 'error-field': getFieldError(scope.$index, 'cdn_type') }"
              >
                <el-option label="PCDN" value="1"></el-option>
                <el-option label="LCDN" value="2"></el-option>
              </el-select>
            </template>
          </el-table-column>

          <el-table-column label="模块" width="180">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.module"
                placeholder="模块"
                size="small"
                :class="{ 'error-field': getFieldError(scope.$index, 'module') }"
              ></el-input>
            </template>
          </el-table-column>

          <el-table-column label="标签信息" width="300">
            <template slot-scope="scope">
              <div class="batch-tag-infos">
                <div v-for="(tag, tagIndex) in scope.row.tag_infos" :key="tagIndex" class="batch-tag-row">
                  <el-select
                    v-model="tag.tag_key"
                    placeholder="标签键"
                    size="small"
                    style="width: 45%; margin-right: 5px"
                    @change="onTagKeyChange(scope.$index, tagIndex)"
                    filterable
                  >
                    <el-option
                      v-for="tagOption in tagKeyOptions"
                      :key="tagOption.tag_key"
                      :label="tagOption.tag_key"
                      :value="tagOption.tag_key"
                    ></el-option>
                  </el-select>
                  <el-select
                    v-model="tag.tag_value"
                    placeholder="标签值"
                    size="small"
                    style="width: 45%; margin-right: 5px"
                    filterable
                  >
                    <el-option
                      v-for="value in getTagValueOptions(tag.tag_key)"
                      :key="value"
                      :label="value"
                      :value="value"
                    ></el-option>
                  </el-select>
                  <el-button
                    v-if="scope.row.tag_infos.length > 1"
                    type="danger"
                    size="mini"
                    @click="removeTagInfo(scope.$index, tagIndex)"
                    icon="el-icon-delete"
                  ></el-button>
                </div>
                <el-button type="text" size="small" @click="addTagInfo(scope.$index)">+ 添加标签</el-button>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="资源组" width="100">
            <template slot-scope="scope">
              <el-input v-model="scope.row.resource_group" placeholder="资源组" size="small"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="RAP标签" width="120">
            <template slot-scope="scope">
              <el-input v-model="scope.row.rap_tag_infos" placeholder="RAP标签" size="small"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="LVS模式" width="100">
            <template slot-scope="scope">
              <el-input v-model="scope.row.lvs_mode" placeholder="LVS模式" size="small"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="端口" width="100">
            <template slot-scope="scope">
              <el-input v-model="scope.row.port" placeholder="端口" size="small"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="备注" width="120">
            <template slot-scope="scope">
              <el-input v-model="scope.row.remark" placeholder="备注" size="small"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="80" align="center">
            <template slot-scope="scope">
              <el-button
                type="danger"
                size="mini"
                @click="removeRow(scope.$index)"
                :disabled="batchForms.length <= 1"
                icon="el-icon-delete"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button type="primary" :loading="submitting" @click="onSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from "@/views/host-tag-config/http.js"

export default {
  name: "tag-rule-dialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    currentRowData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      submitting: false,
      tagKeyOptions: [], // 标签键选项
      tagValueMap: {}, // 标签键对应的标签值映射
      formData: {}, // 单个表单数据
      validationErrors: [] // 验证错误信息
    };
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '修改标签规则' : '新增标签规则';
    },
    headerTitle() {
      return this.isEdit ? '修改标签规则' : '新增标签规则';
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm();
        this.loadTagOptions();
      }
    }
  },
  methods: {
    initForm() {
      this.validationErrors = [];

      if (this.isEdit && this.currentRowData.id) {
        // 编辑模式，将当前数据填充到批量表单数组中
        this.batchForms = [this.createFormFromData(this.currentRowData)];
      } else if (this.isBatch && this.selectedRows.length > 0) {
        // 批量修改模式，将选中的数据填充到批量表单数组中
        this.batchForms = this.selectedRows.map(row => this.createFormFromData(row));
      } else {
        // 新增模式，初始化空表单
        this.batchForms = [this.createEmptyForm()];
      }
    },
    createFormFromData(data) {
      return {
        id: data.id,
        cdn_type: data.cdn_type || '',
        module: data.module || '',
        tag_infos: data.tag_infos && data.tag_infos.length
          ? JSON.parse(JSON.stringify(data.tag_infos))
          : [{ tag_key: '', tag_value: '' }],
        resource_group: data.resource_group || '',
        rap_tag_infos: data.rap_tag_infos || '',
        lvs_mode: data.lvs_mode || '',
        port: data.port || '',
        remark: data.remark || ''
      };
    },
    createEmptyForm() {
      return {
        cdn_type: '',
        module: '',
        tag_infos: [{ tag_key: '', tag_value: '' }],
        resource_group: '',
        rap_tag_infos: '',
        lvs_mode: '',
        port: '',
        remark: ''
      };
    },

    addRow() {
      this.batchForms.push(this.createEmptyForm());
    },
    removeRow(index) {
      if (this.batchForms.length > 1) {
        this.batchForms.splice(index, 1);
      }
    },
    addTagInfo(rowIndex) {
      this.batchForms[rowIndex].tag_infos.push({ tag_key: '', tag_value: '' });
    },
    removeTagInfo(rowIndex, tagIndex) {
      if (this.batchForms[rowIndex].tag_infos.length > 1) {
        this.batchForms[rowIndex].tag_infos.splice(tagIndex, 1);
      }
    },
    onTagKeyChange(rowIndex, tagIndex) {
      // 当标签键改变时，清空对应的标签值
      this.batchForms[rowIndex].tag_infos[tagIndex].tag_value = '';
    },
    async loadTagOptions() {
      try {
        // 获取标签配置列表，用于联动选择
        // 设置足够大的page_size以确保获取全量数据
        const params = {
          page_size: 10000  // 设置足够大的值以获取全量数据
        };
        const res = await http.getTagList(params);
        if (res && res.code === 100000 && res.data && res.data.items) {
          this.tagKeyOptions = res.data.items;

          // 构建标签键值映射
          this.tagValueMap = {};
          res.data.items.forEach(item => {
            if (item.tag_key && item.tag_value_list) {
              this.tagValueMap[item.tag_key] = item.tag_value_list;
            }
          });
        }
      } catch (error) {
        console.error('加载标签选项失败:', error);
      }
    },
    getTagValueOptions(tagKey) {
      return this.tagValueMap[tagKey] || [];
    },
    validateForms() {
      this.validationErrors = [];

      this.batchForms.forEach((form, index) => {
        if (!form.cdn_type) {
          this.validationErrors.push(`第${index + 1}行：请选择CDN类型`);
        }
        if (!form.module) {
          this.validationErrors.push(`第${index + 1}行：请输入模块`);
        }
        const hasEmptyTag = form.tag_infos.some(tag => !tag.tag_key || !tag.tag_value);
        if (hasEmptyTag) {
          this.validationErrors.push(`第${index + 1}行：请完善标签信息`);
        }
      });

      return this.validationErrors.length === 0;
    },
    getFieldError(rowIndex, fieldName) {
      return this.validationErrors.some(error =>
        error.includes(`第${rowIndex + 1}行`) && error.includes(this.getFieldLabel(fieldName))
      );
    },
    getFieldLabel(fieldName) {
      const labels = {
        cdn_type: 'CDN类型',
        module: '模块',
        tag_infos: '标签信息'
      };
      return labels[fieldName] || fieldName;
    },
    async onSubmit() {
      // 验证表单数据
      if (!this.validateForms()) {
        this.$message.error(this.validationErrors[0]);
        return;
      }

      this.submitting = true;
      try {
        await this.handleSubmit();
      } catch (error) {
        console.error('提交失败:', error);
        this.$message.error('提交失败，请重试');
      } finally {
        this.submitting = false;
      }
    },
    async handleSubmit() {
      const operator = window.localStorage.getItem('userInfo') || 'system';

      // 根据是否有id字段判断是新增还是修改
      const hasId = this.batchForms.some(form => form.id);

      if (hasId) {
        // 修改操作
        await this.handleUpdate(operator);
      } else {
        // 新增操作
        await this.handleCreate(operator);
      }
    },
    async handleCreate(operator) {
      const createPromises = this.batchForms.map(form => {
        const data = {
          cdn_type: form.cdn_type,
          module: form.module,
          tag_infos: form.tag_infos,
          resource_group: form.resource_group || '',
          rap_tag_infos: form.rap_tag_infos || '',
          lvs_mode: form.lvs_mode || '',
          port: form.port || '',
          remark: form.remark || '',
          operator
        };
        return http.addTagRule(data);
      });

      await Promise.all(createPromises);
      this.$message.success(`新增成功，共新增${this.batchForms.length}条记录`);
      this.$emit('refresh');
      this.$emit('close');
    },
    async handleUpdate(operator) {
      const updatePromises = this.batchForms.map(form => {
        const data = {
          id: form.id,
          cdn_type: form.cdn_type,
          module: form.module,
          tag_infos: form.tag_infos,
          resource_group: form.resource_group || '',
          rap_tag_infos: form.rap_tag_infos || '',
          lvs_mode: form.lvs_mode || '',
          port: form.port || '',
          remark: form.remark || '',
          operator
        };
        return http.updateTagRule(data);
      });

      await Promise.all(updatePromises);
      const message = this.isBatch
        ? `批量修改成功，共修改${this.batchForms.length}条记录`
        : '修改成功';
      this.$message.success(message);
      this.$emit('refresh');
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.batch-container {
  margin-top: 10px;
}

.batch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  h4 {
    margin: 0;
    color: #303133;
  }
}

.batch-table {
  max-height: 400px;
  overflow-y: auto;

  .el-table {
    font-size: 12px;
  }
}

.batch-tag-infos {
  .batch-tag-row {
    display: flex;
    align-items: center;
    margin-bottom: 5px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.error-field {
  border-color: #f56c6c !important;
}
</style>
