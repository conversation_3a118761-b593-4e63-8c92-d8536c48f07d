<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    width="1200px"
    :close-on-click-modal="false"
    :show-close="true"
    @close="$emit('close')"
    append-to-body
  >
    <!-- 批量操作表单 -->
    <div class="batch-container">
      <!-- 表头区域：标题和新增按钮 -->
      <div class="batch-header" v-if="!isEdit">
        <el-button
          type="primary"
          size="small"
          @click="addRow"
        >
          新增一行
        </el-button>
      </div>

      <!-- 批量表单表格 -->
      <el-table
        :data="batchForms"
        border
        style="width: 100%"
        max-height="400"
      >
        <!-- IP地址列 -->
        <el-table-column label="IP地址" width="200">
          <template slot-scope="scope">
            <el-input
              v-if="!isEdit"
              v-model="scope.row.ip"
              placeholder="请输入IP地址"
              size="small"
              :class="{ 'error-input': validationErrors[scope.$index] && validationErrors[scope.$index].ip }"
            ></el-input>
            <span v-else class="readonly-text">{{ scope.row.ip }}</span>
          </template>
        </el-table-column>

        <!-- CDN类型列 -->
        <el-table-column label="CDN类型" width="150">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.cdn_type"
              placeholder="请选择"
              size="small"
              style="width: 100%"
              :class="{ 'error-input': validationErrors[scope.$index] && validationErrors[scope.$index].cdn_type }"
              allow-create
              filterable
            >
              <el-option
                v-for="item in cdnTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>

        <!-- 模块列 -->
        <el-table-column label="模块" width="180">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.module"
              placeholder="请选择"
              size="small"
              style="width: 100%"
              :class="{ 'error-input': validationErrors[scope.$index] && validationErrors[scope.$index].module }"
              allow-create
              filterable
            >
              <el-option
                v-for="item in moduleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>

        <!-- 标签信息列 -->
        <el-table-column label="标签信息" min-width="300">
          <template slot-scope="scope">
            <div class="tag-container">
              <div
                v-for="(tag, tagIndex) in scope.row.tag_infos"
                :key="tagIndex"
                class="tag-row"
              >
                <el-select
                  v-model="tag.tag_key"
                  placeholder="选择标签键"
                  size="small"
                  style="width: 120px; margin-right: 8px;"
                  @change="onTagKeyChange(scope.$index, tagIndex)"
                  :class="{ 'error-input': validationErrors[scope.$index] && validationErrors[scope.$index][`tag_key_${tagIndex}`] }"
                >
                  <el-option
                    v-for="item in tagOptions"
                    :key="item.tag_key"
                    :label="item.tag_key"
                    :value="item.tag_key"
                  ></el-option>
                </el-select>

                <el-select
                  v-model="tag.tag_value"
                  placeholder="选择标签值"
                  size="small"
                  style="width: 120px; margin-right: 8px;"
                  :class="{ 'error-input': validationErrors[scope.$index] && validationErrors[scope.$index][`tag_value_${tagIndex}`] }"
                >
                  <el-option
                    v-for="value in getTagValues(tag.tag_key)"
                    :key="value"
                    :label="value"
                    :value="value"
                    :disabled="isTagValueDisabled(scope.$index, tagIndex, tag.tag_key, value)"
                  ></el-option>
                </el-select>

                <el-button
                  type="danger"
                  size="mini"
                  icon="el-icon-delete"
                  @click="removeTag(scope.$index, tagIndex)"
                  v-if="scope.row.tag_infos.length > 1"
                ></el-button>
              </div>

              <el-button
                type="primary"
                size="mini"
                icon="el-icon-plus"
                @click="addTag(scope.$index)"
                style="margin-top: 5px;"
              >
                添加标签
              </el-button>
            </div>
          </template>
        </el-table-column>

        <!-- 备注列 -->
        <el-table-column label="备注" width="150">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.remark"
              placeholder="请输入备注"
              size="small"
              type="textarea"
              :rows="2"
            ></el-input>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="80" v-if="!isEdit">
          <template slot-scope="scope">
            <el-button
              type="danger"
              size="mini"
              icon="el-icon-delete"
              @click="removeRow(scope.$index)"
              v-if="batchForms.length > 1"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 验证错误提示 -->
      <div v-if="hasValidationErrors" class="error-tips">
        <el-alert
          title="请检查并修正以下错误："
          type="error"
          :closable="false"
          style="margin-top: 10px;"
        >
          <ul>
            <li v-for="(errors, index) in validationErrors" :key="index">
              <span v-if="Object.keys(errors).length > 0">
                第 {{ index + 1 }} 行：
                <span v-for="(error, field) in errors" :key="field">{{ error }} </span>
              </span>
            </li>
          </ul>
        </el-alert>
      </div>
    </div>

    <!-- 对话框底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('close')">取消</el-button>
      <el-button
        type="primary"
        :loading="submitting"
        @click="handleSubmit"
      >
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from "@/views/host-tag-config/http.js";

export default {
  name: "host-tag-dialog",
  props: {
    // 对话框显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 是否为编辑模式
    isEdit: {
      type: Boolean,
      default: false
    },
    // 是否为批量操作
    isBatch: {
      type: Boolean,
      default: false
    },
    // 当前编辑的行数据
    currentRowData: {
      type: Object,
      default: () => ({})
    },
    // 选中的行数据（批量操作时使用）
    selectedRows: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      submitting: false,
      batchForms: [], // 批量表单数组
      validationErrors: [], // 验证错误信息数组
      
      // 选项数据
      cdnTypeOptions: [],
      moduleOptions: [],
      tagOptions: [],
    };
  },
  computed: {
    // 对话框标题
    dialogTitle() {
      if (this.isEdit) {
        return this.isBatch ? '批量修改主机标签' : '修改主机标签';
      } else {
        return '新增主机标签';
      }
    },
    
    // 是否有验证错误
    hasValidationErrors() {
      return this.validationErrors.some(errors => Object.keys(errors).length > 0);
    },
  },
  watch: {
    // 监听对话框显示状态，显示时初始化表单
    visible(val) {
      if (val) {
        this.initForm();
        this.loadOptions();
      }
    }
  },
  methods: {
    // 加载选项数据
    async loadOptions() {
      try {
        // 加载CDN类型选项
        const cdnTypeRes = await http.getCdnTypeOptions();
        if (cdnTypeRes && cdnTypeRes.code === 100000) {
          this.cdnTypeOptions = cdnTypeRes.data || [];
        }
        
        // 加载模块选项
        const moduleRes = await http.getModuleOptions();
        if (moduleRes && moduleRes.code === 100000) {
          this.moduleOptions = moduleRes.data || [];
        }
        
        // 加载标签选项
        const tagRes = await http.getTagList();
        if (tagRes) {
          this.tagOptions = tagRes.data.items || [];
        }
      } catch (error) {
        console.error("加载选项数据失败:", error);
      }
    },
    
    // 初始化表单数据
    initForm() {
      this.validationErrors = [];
      
      if (this.isEdit && this.isBatch) {
        // 批量修改模式，将选中的数据填充到批量表单数组中
        this.batchForms = this.selectedRows.map(row => this.createFormFromData(row));
      } else if (this.isEdit && this.currentRowData && this.currentRowData.ip) {
        // 单个修改模式，将当前数据填充到批量表单数组中
        this.batchForms = [this.createFormFromData(this.currentRowData)];
      } else {
        // 新增模式，初始化空表单
        this.batchForms = [this.createEmptyForm()];
      }
    },
    
    // 从数据创建表单对象
    createFormFromData(data) {
      return {
        ip: data.ip || "",
        cdn_type: data.cdn_type || "",
        module: data.module || "",
        tag_infos: data.tag_values && data.tag_values.length > 0 
          ? data.tag_values.map(tag => ({
              tag_key: tag.tag_key || "",
              tag_value: tag.tag_value || ""
            }))
          : [{ tag_key: "", tag_value: "" }],
        remark: data.remark || "",
      };
    },
    
    // 创建空表单对象
    createEmptyForm() {
      return {
        ip: "",
        cdn_type: "",
        module: "",
        tag_infos: [{ tag_key: "", tag_value: "" }],
        remark: "",
      };
    },

    // 获取标签值选项
    getTagValues(tagKey) {
      const tagOption = this.tagOptions.find(option => option.tag_key === tagKey);
      return tagOption ? tagOption.tag_value_list || [] : [];
    },

    // 检查标签值是否应该被禁用（重复验证）
    isTagValueDisabled(rowIndex, tagIndex, tagKey, tagValue) {
      if (!tagKey || !tagValue) return false;

      const currentForm = this.batchForms[rowIndex];
      if (!currentForm || !currentForm.tag_infos) return false;

      // 检查当前行中是否已存在相同的标签键值组合
      return currentForm.tag_infos.some((tag, index) => {
        return index !== tagIndex &&
               tag.tag_key === tagKey &&
               tag.tag_value === tagValue;
      });
    },

    // 标签键变化时的处理
    onTagKeyChange(rowIndex, tagIndex) {
      // 清空标签值
      this.batchForms[rowIndex].tag_infos[tagIndex].tag_value = "";
    },

    // 添加新行
    addRow() {
      this.batchForms.push(this.createEmptyForm());
      this.validationErrors.push({});
    },

    // 删除行
    removeRow(index) {
      if (this.batchForms.length > 1) {
        this.batchForms.splice(index, 1);
        this.validationErrors.splice(index, 1);
      }
    },

    // 添加标签
    addTag(rowIndex) {
      this.batchForms[rowIndex].tag_infos.push({ tag_key: "", tag_value: "" });
    },

    // 删除标签
    removeTag(rowIndex, tagIndex) {
      if (this.batchForms[rowIndex].tag_infos.length > 1) {
        this.batchForms[rowIndex].tag_infos.splice(tagIndex, 1);
      }
    },

    // 验证表单
    validateForm() {
      this.validationErrors = [];
      let hasError = false;

      this.batchForms.forEach((form, formIndex) => {
        const errors = {};

        // 验证IP地址
        if (!form.ip || form.ip.trim() === "") {
          errors.ip = "IP地址不能为空";
          hasError = true;
        } else if (!/^(\d{1,3}\.){3}\d{1,3}$/.test(form.ip.trim())) {
          errors.ip = "IP地址格式不正确";
          hasError = true;
        }

        // 验证CDN类型
        if (!form.cdn_type || form.cdn_type === "") {
          errors.cdn_type = "CDN类型不能为空";
          hasError = true;
        }

        // 验证模块
        if (!form.module || form.module.trim() === "") {
          errors.module = "模块不能为空";
          hasError = true;
        }

        // 验证标签信息
        const tagCombinations = new Set();
        form.tag_infos.forEach((tag, tagIndex) => {
          if (!tag.tag_key || tag.tag_key.trim() === "") {
            errors[`tag_key_${tagIndex}`] = `标签键${tagIndex + 1}不能为空`;
            hasError = true;
          }
          if (!tag.tag_value || tag.tag_value.trim() === "") {
            errors[`tag_value_${tagIndex}`] = `标签值${tagIndex + 1}不能为空`;
            hasError = true;
          }

          // 检查标签重复
          if (tag.tag_key && tag.tag_value) {
            const combination = `${tag.tag_key}:${tag.tag_value}`;
            if (tagCombinations.has(combination)) {
              errors[`tag_duplicate_${tagIndex}`] = `标签组合"${tag.tag_key}:${tag.tag_value}"重复`;
              hasError = true;
            } else {
              tagCombinations.add(combination);
            }
          }
        });

        this.validationErrors.push(errors);
      });

      return !hasError;
    },

    // 提交表单
    async handleSubmit() {
      if (!this.validateForm()) {
        this.$message.error("请检查表单数据");
        return;
      }

      this.submitting = true;

      try {
        const operator = window.localStorage.getItem("userInfo") || "system";

        if (this.isEdit) {
          // 修改操作
          for (const form of this.batchForms) {
            const params = {
              ips: [form.ip],
              cdn_type: form.cdn_type,
              module: form.module,
              tag_infos: form.tag_infos.map(tag => ({
                tag_key: tag.tag_key,
                tag_value: tag.tag_value
              })),
              remark: form.remark || "",
              operator: operator,
            };

            const res = await http.updateHostTag(params);
            if (!res || res.code !== 100000) {
              throw new Error(`修改IP ${form.ip} 失败`);
            }
          }

          this.$message.success(this.isBatch ? "批量修改成功" : "修改成功");
        } else {
          // 新增操作
          for (const form of this.batchForms) {
            const params = {
              ips: [form.ip],
              cdn_type: form.cdn_type,
              module: form.module,
              tag_infos: form.tag_infos.map(tag => ({
                tag_key: tag.tag_key,
                tag_value: tag.tag_value
              })),
              remark: form.remark || "",
              operator: operator,
            };

            const res = await http.createHostTag(params);
            if (!res || res.code !== 100000) {
              throw new Error(`新增IP ${form.ip} 失败`);
            }
          }

          this.$message.success("新增成功");
        }

        this.$emit("close");
        this.$emit("refresh");
      } catch (error) {
        console.error("提交失败:", error);
        this.$message.error(error.message || "操作失败");
      } finally {
        this.submitting = false;
      }
    },
  },
};
</script>

<style scoped>
.batch-container {
  max-height: 500px;
  overflow-y: auto;
}

.batch-header {
  margin-bottom: 15px;
  text-align: right;
}

.tag-container {
  padding: 5px;
}

.tag-row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.error-input {
  border-color: #f56c6c !important;
}

.error-tips ul {
  margin: 0;
  padding-left: 20px;
}

.error-tips li {
  margin-bottom: 5px;
}

.readonly-text {
  color: #606266;
  font-size: 14px;
  line-height: 32px;
  padding: 0 8px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: inline-block;
  width: 100%;
  box-sizing: border-box;
}
</style>
