import { ajax } from "@/api/ajax";
import { formPromise } from "@/utils/fetch";

const prefix = "/api/v1"
export default {
  //标签配置相关API
  async getTagList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/tag/list`, {
      params
    }))
  },
  async addTag(data) {
    return await formPromise(ajax.post(`${prefix}/sda/tag/create`, data))
  },
  
  async updateTag(data) {
    return await formPromise(ajax.patch(`${prefix}/sda/tag/update`, data))
  },
  
  async deleteTag(id, params) {
    return await formPromise(ajax.delete(`${prefix}/sda/tag/delete/${id}`, { params }))
  },

  async getIpList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/host_tag/host/list`, {
      params
    }))
  },
  
  // Node带宽数据源配置相关API
  async getNodeBandwidthSourceList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/node_bw_source_config/get`, {
      params
    }))
  },
  
  async addNodeBandwidthSource(data) {
    return await formPromise(ajax.post(`${prefix}/sda/node_bw_source_config/create`, data))
  },
  
  async updateNodeBandwidthSource(data) {
    return await formPromise(ajax.post(`${prefix}/sda/node_bw_source_config/update`, data))
  },
  
  async deleteNodeBandwidthSource(id) {
    return await formPromise(ajax.delete(`${prefix}/sda/node_bw_source_config/delete/${id}`))
  },

  // 标签规则配置相关API
  async getTagRuleList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/tag_rule/list`, {
      params
    }))
  },

  async addTagRule(data) {
    return await formPromise(ajax.post(`${prefix}/sda/tag_rule/create`, data))
  },

  async updateTagRule(data) {
    return await formPromise(ajax.patch(`${prefix}/sda/tag_rule/update`, data))
  },

  async deleteTagRule(id) {
    return await formPromise(ajax.delete(`${prefix}/sda/tag_rule/delete/${id}`))
  },

  // 主机标签配置相关API
  async getHostTagList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/host_tag/list`, {
      params
    }))
  },

  async createHostTag(data) {
    return await formPromise(ajax.post(`${prefix}/sda/host_tag/create`, data))
  },

  async updateHostTag(data) {
    return await formPromise(ajax.patch(`${prefix}/sda/host_tag/update`, data))
  },

  async deleteHostTag(data) {
    return await formPromise(ajax.delete(`${prefix}/sda/host_tag/delete`, { data }))
  },

  // 获取CDN类型选项
  async getCdnTypeOptions() {
    return await formPromise(ajax.get(`${prefix}/sda/cdn_type/options`))
  },

  // 获取模块选项
  async getModuleOptions() {
    return await formPromise(ajax.get(`${prefix}/sda/module/options`))
  },
}
